import { useState } from "react";
import "@fontsource/caveat/500.css";
import { ALargeSmall, Signature } from "lucide-react";

export default function SignatureMethod() {
  const [typedSignature, setSignature] = useState("");
  const [signatureMethod, setSignatureMethod] = useState("type"); // Track signature method

  const toggleSignatureMethod = () => {
    if (signatureMethod === "type") {
      setSignature("");
    }
    setSignatureMethod(signatureMethod === "type" ? "draw" : "type");
  };

  return (
    <div className="space-y-4">
      <div className="w-full">
        <label className="block text-sm font-bold text-slate-800 mb-3">Signature: </label>

        {/* Signature Display Area */}
        <div className="w-full h-64 relative border-2 border-slate-300 rounded-lg bg-white mb-4 flex items-center justify-center">
          <div id="signature-box" className="w-full h-full flex items-center justify-center ">
            {signatureMethod === "type" ? (
              <input
                id="signature"
                name="signature"
                type="text"
                placeholder="Type your name"
                autocomplete="none"
                aria-autocomplete="none"
                value={typedSignature}
                onChange={(e) => setSignature(e.target.value)}
                className="w-full h-full font-caveat text-6xl placeholder:text-gray-500 p-4 border-0 bg-transparent text-center focus:outline-none focus:ring-0"
                required
              />
            ) : (
              <div className="font-caveat text-6xl w-full h-full flex items-center justify-center text-gray-500">
                Sign here
              </div>
            )}
          </div>
          <div className="absolute w-1/2 h-0.5 top-2/3 bg-gray-400 rounded-lg" />
        </div>

        {/* Signature Method Buttons */}
        <div className="flex gap-4">
          <button
            type="button"
            onClick={toggleSignatureMethod}
            className={`flex-1 py-3 px-6 rounded-lg border-2 font-medium transition-colors ${
              signatureMethod === "type"
                ? "border-blue-500 bg-blue-50 text-blue-700"
                : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"
            }`}
          >
            <span className="flex justify-center gap-4">
              <ALargeSmall /> Type signature
            </span>
          </button>
          <button
            type="button"
            onClick={toggleSignatureMethod}
            className={`flex-1 py-3 px-6 rounded-lg border-2 font-medium transition-colors ${
              signatureMethod === "draw"
                ? "border-blue-500 bg-blue-50 text-blue-700"
                : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"
            }`}
          >
            <span className="flex justify-center gap-4">
              <Signature /> Draw signature
            </span>
          </button>
        </div>
      </div>
      <div className="w-full max-w-xs">
        <label className="block text-sm font-bold text-slate-800 mb-2">Date of Signing: </label>
        <div className="w-full p-3 border border-slate-300 rounded-lg text-sm font-semibold text-slate-800 shadow-sm">
          {new Date().toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </div>
      </div>
    </div>
  );
}
